@import url('https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Bebas+Neue&display=swap');
/* @import "tailwindcss"; */
@tailwind base;
@tailwind components;
@tailwind utilities;
/* @tailwind base;
@tailwind components;
@tailwind utilities;
@theme {
  --color-five:#0158A6;
  --color-six:#121212;
  --color-seven:#3D550C;
  --color-eight:#FF56A5;
  --color-nine:#0a0f14;
  --color-ten:#CFE5FF;
  --color-primary:#AEBB1E;
  --color-black: #0f0f0f;
} */
.glowing-yellow{
  filter: drop-shadow(0 0 7px #F5FF1E);
}
.glowing-green{
  filter: drop-shadow(0 0 7px #D4FF00);
}
.glowing-pink{
  filter: drop-shadow(0 0 7px #FF56A5);
}
.glow-green {
  box-shadow: 
    0 0 35px rgba(212, 255, 0, 0.4), 
    0 0 17px rgba(212, 255, 0, 0.3), 
    0 0 10px rgba(174, 187, 30, 0.2);
}

.glow-hover:hover {
  box-shadow: 
    0 0 35px rgba(212, 255, 0, 0.4), 
    0 0 17px rgba(212, 255, 0, 0.3), 
    0 0 10px rgba(174, 187, 30, 0.2);
}

.glow-pink {
  box-shadow: 
    0 0 35px rgba(255, 86, 165, 0.4), 
    0 0 17px rgba(255, 86, 165, 0.3), 
    0 0 10px rgba(255, 86, 165, 0.2);
}
.fading{
  height: 167px;
  width: 100%;
  z-index: 50;
  margin: -100px 0px 0px 0px;
  position: relative;
  background: linear-gradient(0deg, rgba(15, 15, 15, 1) 50%, rgba(31, 31, 31, 0) 100%);
}
.roboto {
  font-family: "Roboto", sans-serif;
}
.inter {
  font-family: "Inter", sans-serif;
}
.bebas {
  font-family: "Bebas Neue", sans-serif;
}
body{
  scroll-behavior: smooth;
  padding-top: 80px; /* Add space for fixed header */
}
* {
  margin: 0;
  padding: 0;
  font-family: "Right Grotesk", sans-serif;
}

@font-face {
  font-family: "Right Grotesk";
  src: url("/path/to/RightGrotesk-Regular.woff2") format("woff2"),
    url("/path/to/RightGrotesk-Regular.woff") format("woff");
  font-weight: normal;
  font-style: normal;
}

body {
  font-family: "Right Grotesk", sans-serif;
}

:root {
  --color-black: #0f0f0f;
  --color-black-2: #0d0d0d;
  --color-white: #fff;
  --color-yellow: #aebb1e;
  --color-primary: #AEBB1E;
  --color-blue: #9bd4d7;
  --color-gray-800: #1a1a1a;
  --color-gray-700: #2a2a2a;
  --color-gray-600: #3a3a3a;
  --tr-main-tf: cubic-bezier(0.36, 0.3, 0, 1);
  --tr-main-dur: 300ms;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background-color: black;
}

::-webkit-scrollbar-thumb {
  background-color: var(--color-white);
  border-radius: 4px;
}

html {
  scroll-behavior: smooth;
}

.slide-in {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #0d0d0d;
  transform-origin: bottom;
}

.slide-out {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: #0d0d0d;
  transform-origin: top;
}

/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .text-responsive {
    font-size: clamp(1.5rem, 4vw, 3rem);
  }

  .grid-responsive {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .flex-responsive {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .text-responsive-sm {
    font-size: clamp(1rem, 3vw, 1.5rem);
  }

  .padding-responsive {
    padding: 1rem;
  }

  .margin-responsive {
    margin: 0.5rem 0;
  }
}

/* Techrypt Brand Colors */
.bg-techrypt-primary {
  background-color: var(--color-primary);
}

.text-techrypt-primary {
  color: var(--color-primary);
}

.border-techrypt-primary {
  border-color: var(--color-primary);
}

.hover-techrypt-primary:hover {
  background-color: var(--color-primary);
  color: var(--color-black);
}

/* Enhanced Button Styles */
.btn-techrypt {
  background-color: var(--color-primary);
  color: var(--color-black);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.btn-techrypt:hover {
  background-color: rgba(174, 187, 30, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(174, 187, 30, 0.3);
}

.btn-techrypt-outline {
  background-color: transparent;
  color: var(--color-primary);
  border: 2px solid var(--color-primary);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
}

.btn-techrypt-outline:hover {
  background-color: var(--color-primary);
  color: var(--color-black);
}

/* Loading and Animation Enhancements */
.fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
