.aboutpara-container {
  display: flex;
  justify-content: center;
  background-color: var(--color-black);
  padding: 20px;
  margin-top: -100px;
}
.aboutpara {
  width: 75vw;
  display: flex;
  flex-direction: column;
  align-items: center;
}
.heading-para {
  margin-bottom: 20px;
}
.heading-para h2 {
  color: white;
  text-align: center;
  font-size: 2.5rem;
}
.seperator {
  height: 50vh;
  width: 2px;
  background-color: white;
  margin-bottom: 20px;
}
.bordered-para {
  margin-bottom: 20px;
}
.bordered-para h3 {
  color: white;
  border: 2px solid white;
  border-radius: 30px;
  padding: 8px;
  text-align: center;
}
.para {
  width: 65vw;
}
.para p {
  color: white;
  text-align: center;
  font-size: 1.4rem;
}
@media only screen and (max-width: 600px) {
  .heading-para h2 {
    margin-top: 100px;
    font-size: 1.5rem;
    font-weight: 100;
  }
  .bordered-para h3 {
    font-size: 1rem;
  }
}
