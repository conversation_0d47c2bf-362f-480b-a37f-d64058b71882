#!/usr/bin/env python3
"""
🌐 WEB DATABASE VIEWER
Simple web interface to check database status when direct connection fails
"""

from flask import Flask, jsonify, render_template_string
from datetime import datetime
import json

app = Flask(__name__)

# HTML template for database viewer
HTML_TEMPLATE = """
<!DOCTYPE html>
<html>
<head>
    <title>Techrypt Database Viewer</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .header { text-align: center; color: #333; margin-bottom: 30px; }
        .status-card { background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0; }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .warning { border-left: 4px solid #ffc107; }
        .info { border-left: 4px solid #17a2b8; }
        .refresh-btn { background: #007bff; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; }
        .refresh-btn:hover { background: #0056b3; }
        .timestamp { color: #666; font-size: 0.9em; }
        .data-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .data-table th { background: #f2f2f2; }
        .json-data { background: #f8f9fa; padding: 10px; border-radius: 3px; font-family: monospace; font-size: 0.9em; }
    </style>
    <script>
        function refreshData() {
            location.reload();
        }
        
        function checkStatus() {
            fetch('/api/status')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('status-content').innerHTML = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    document.getElementById('status-content').innerHTML = 'Error: ' + error;
                });
        }
        
        setInterval(checkStatus, 30000); // Auto-refresh every 30 seconds
    </script>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Techrypt Database Viewer</h1>
            <p class="timestamp">Last Updated: {{ timestamp }}</p>
            <button class="refresh-btn" onclick="refreshData()">🔄 Refresh</button>
        </div>
        
        <div class="status-card {{ status_class }}">
            <h3>📊 Database Connection Status</h3>
            <p><strong>Status:</strong> {{ db_status }}</p>
            <p><strong>Message:</strong> {{ db_message }}</p>
        </div>
        
        <div class="status-card info">
            <h3>🌐 Application Status</h3>
            <p><strong>Frontend:</strong> <span id="frontend-status">Checking...</span></p>
            <p><strong>Backend:</strong> ✅ Running (you're viewing this page)</p>
            <p><strong>API Endpoints:</strong> Available</p>
        </div>
        
        <div class="status-card info">
            <h3>📋 Available Actions</h3>
            <ul>
                <li><a href="/api/status">📊 Check Database Status (JSON)</a></li>
                <li><a href="/api/test-connection">🔧 Test Connection</a></li>
                <li><a href="http://localhost:5173" target="_blank">🖥️ Open Frontend</a></li>
                <li><a href="https://cloud.mongodb.com" target="_blank">☁️ MongoDB Atlas Dashboard</a></li>
            </ul>
        </div>
        
        <div class="status-card warning">
            <h3>💡 Troubleshooting Tips</h3>
            <ul>
                <li><strong>SSL Handshake Error:</strong> Usually temporary - wait 5-10 minutes and refresh</li>
                <li><strong>Network Issues:</strong> Check your internet connection</li>
                <li><strong>Atlas Maintenance:</strong> Check MongoDB Atlas dashboard for notifications</li>
                <li><strong>IP Whitelist:</strong> Ensure your IP is whitelisted in Atlas</li>
            </ul>
        </div>
        
        <div class="status-card info">
            <h3>📊 Real-time Status Monitor</h3>
            <pre id="status-content" class="json-data">Loading...</pre>
        </div>
    </div>
    
    <script>
        // Check frontend status
        fetch('http://localhost:5173')
            .then(response => {
                document.getElementById('frontend-status').innerHTML = '✅ Running';
            })
            .catch(error => {
                document.getElementById('frontend-status').innerHTML = '❌ Not Running';
            });
        
        // Initial status check
        checkStatus();
    </script>
</body>
</html>
"""

@app.route('/')
def dashboard():
    """Main dashboard"""
    try:
        from db import get_collection
        
        # Try to connect to database
        try:
            users_collection = get_collection('users')
            count = users_collection.count_documents({})
            db_status = "✅ Connected"
            db_message = f"Database accessible - {count} users found"
            status_class = "success"
        except Exception as e:
            db_status = "❌ Connection Failed"
            db_message = f"Error: {str(e)[:100]}..."
            status_class = "error"
            
    except ImportError:
        db_status = "❌ Import Error"
        db_message = "Cannot import database module"
        status_class = "error"
    
    return render_template_string(HTML_TEMPLATE, 
                                timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                                db_status=db_status,
                                db_message=db_message,
                                status_class=status_class)

@app.route('/api/status')
def api_status():
    """API endpoint for status check"""
    try:
        from db import get_collection
        
        status = {
            "timestamp": datetime.now().isoformat(),
            "database": {
                "status": "checking",
                "collections": {}
            },
            "application": {
                "backend": "running",
                "frontend": "unknown"
            }
        }
        
        # Test database connection
        try:
            collections = ['users', 'appointments', 'conversations']
            for coll_name in collections:
                collection = get_collection(coll_name)
                count = collection.count_documents({})
                status["database"]["collections"][coll_name] = {
                    "count": count,
                    "status": "accessible"
                }
            
            status["database"]["status"] = "connected"
            status["database"]["message"] = "All collections accessible"
            
        except Exception as e:
            status["database"]["status"] = "error"
            status["database"]["error"] = str(e)
            status["database"]["message"] = "Connection failed"
        
        return jsonify(status)
        
    except Exception as e:
        return jsonify({
            "timestamp": datetime.now().isoformat(),
            "error": str(e),
            "status": "error"
        })

@app.route('/api/test-connection')
def test_connection():
    """Test database connection"""
    try:
        from db import get_collection
        
        # Simple connection test
        users = get_collection('users')
        count = users.count_documents({})
        
        return jsonify({
            "status": "success",
            "message": f"Connection successful - {count} users found",
            "timestamp": datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({
            "status": "error",
            "message": str(e),
            "timestamp": datetime.now().isoformat()
        })

if __name__ == '__main__':
    print("🌐 Starting Techrypt Database Viewer")
    print("📊 Access at: http://localhost:5003")
    print("🔄 Auto-refreshes every 30 seconds")
    print("💡 Use this when direct database connection fails")
    
    app.run(host='0.0.0.0', port=5003, debug=False)
