/* Techrypt Chatbot Styles - Enhanced for Smart ChatGPT-like Experience */

/* Smart Chatbot Container */
.smart-chatbot-container {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 380px;
  height: 600px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  z-index: 50;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e5e7eb;
}

.smart-chatbot-container.closed {
  transform: translateY(100%) scale(0.8);
  opacity: 0;
  pointer-events: none;
}

.smart-chatbot-container.open {
  transform: translateY(0) scale(1);
  opacity: 1;
  pointer-events: all;
}

.smart-chatbot-container.minimized {
  height: 60px;
  overflow: hidden;
}

/* Header with Green Theme */
.chatbot-header {
  padding: 16px 20px;
  border-radius: 16px 16px 0 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);
}

.chatbot-header button {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.chatbot-header button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Messages Container */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #fafafa;
  scroll-behavior: smooth;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #10b981;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #059669;
}

/* Message Styles */
.message {
  margin-bottom: 16px;
  animation: messageSlideIn 0.3s ease-out;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.message.user .message-content {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.message.bot .message-avatar {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

.message.user .message-avatar {
  background: linear-gradient(135deg, #6b7280, #4b5563);
  color: white;
}

.message-text {
  max-width: 280px;
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.5;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.message.bot .message-text {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.message.user .message-text {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 12px 16px;
  background: white;
  border-radius: 18px;
  border: 1px solid #e5e7eb;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #10b981;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}
.techrypt-chatbot-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  z-index: 9999;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 20px;
  pointer-events: none;
}

.techrypt-chatbot-container {
  width: 100%;
  max-width: 320px;
  height: calc(100vh - 40px);
  max-height: 600px;
  background: linear-gradient(135deg, #000000 0%, #0f0f0f 100%);
  border-radius: 8px;
  border: 2px solid #AEBB1E;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(174, 187, 30, 0.2), 0 0 0 1px rgba(174, 187, 30, 0.1);
  margin-top: 20px;
  margin-right: 15px;
  transition: all 0.3s ease;
  pointer-events: auto;
}

/* Minimized state - like a browser tab */
.techrypt-chatbot-overlay.minimized {
  align-items: flex-end;
  justify-content: flex-end;
  padding: 0;
}

.techrypt-chatbot-container.minimized {
  width: 350px;
  height: 60px;
  max-height: 60px;
  border-radius: 12px 12px 0 0;
  margin: 0;
  position: fixed;
  bottom: 0;
  right: 20px;
  box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.3);
  cursor: pointer;
}

.techrypt-chatbot-container.minimized:hover {
  transform: translateY(-2px);
  box-shadow: 0 -8px 25px rgba(0, 0, 0, 0.4);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .techrypt-chatbot-overlay {
    padding: 10px;
    align-items: flex-start;
    justify-content: center;
  }

  .techrypt-chatbot-container {
    width: 100%;
    max-width: 100%;
    height: calc(100vh - 20px);
    max-height: none;
    border-radius: 16px;
    margin-top: 0;
  }

  .techrypt-chatbot-header {
    padding: 16px;
  }

  .techrypt-chatbot-header-content {
    gap: 12px;
  }

  .techrypt-chatbot-avatar {
    width: 40px;
    height: 40px;
    font-size: 20px;
  }

  .techrypt-chatbot-title h3 {
    font-size: 18px;
  }

  .techrypt-chatbot-title p {
    font-size: 11px;
  }

  .techrypt-chatbot-messages {
    padding: 12px;
  }

  .techrypt-message {
    margin-bottom: 12px;
    gap: 8px;
  }

  .techrypt-message-avatar {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .techrypt-message-bubble {
    padding: 12px 16px;
    border-radius: 16px;
  }

  .techrypt-message-bubble p {
    font-size: 14px;
    line-height: 1.5;
  }

  .techrypt-chatbot-input {
    padding: 16px;
  }

  .techrypt-input-container {
    gap: 8px;
  }

  .techrypt-input-container textarea {
    padding: 12px 16px;
    font-size: 14px;
    border-radius: 16px;
  }

  .techrypt-mic-button,
  .techrypt-send-button {
    width: 48px;
    height: 48px;
    font-size: 18px;
    border-radius: 12px;
  }
}

/* Header */
.techrypt-chatbot-header {
  padding: 18px 18px;
  background: linear-gradient(135deg, #AEBB1E 0%, #D3DC5A 100%);
  border-bottom: 1px solid rgba(174, 187, 30, 0.3);
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
  min-height: 78px;
  position: relative;
}

/* Minimized header - tab-like appearance */
.techrypt-chatbot-container.minimized .techrypt-chatbot-header {
  padding: 12px 20px;
  border-bottom: none;
  border-radius: 12px 12px 0 0;
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  cursor: pointer;
}

.techrypt-chatbot-container.minimized .techrypt-chatbot-header:hover {
  background: linear-gradient(135deg, #2a2a2a 0%, #3a3a3a 100%);
}

/* Lower content row */
.techrypt-chatbot-header-content {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  min-width: 0;
  padding: 16px 4px 4px 4px;
  margin-top: auto;
  position: relative;
}

.techrypt-chatbot-avatar {
  width: 32px;
  height: 32px;
  border-radius: 7px;
  background: linear-gradient(135deg, #000000 0%, #0f0f0f 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #AEBB1E;
  font-size: 16px;
  border: 2px solid #AEBB1E;
  flex-shrink: 0;
  margin-right: 2px;
}

.techrypt-chatbot-title h3 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #000000;
  transition: all 0.3s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
  flex: 1;
  min-width: 0;
  letter-spacing: 0.2px;
}

.techrypt-chatbot-title p {
  display: none;
}

/* Minimized title styles */
.techrypt-chatbot-container.minimized .techrypt-chatbot-title h3 {
  font-size: 14px;
  font-weight: 500;
  background: linear-gradient(135deg, #00d4ff 0%, #ff6b35 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.techrypt-chatbot-container.minimized .techrypt-chatbot-avatar {
  width: 32px;
  height: 32px;
  font-size: 16px;
}

/* Upper action row */
.techrypt-chatbot-header-actions {
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: flex-end;
  flex-shrink: 0;
  padding: 2px 4px 8px 0;
  position: absolute;
  top: 12px;
  right: 18px;
}

.techrypt-chatbot-clear,
.techrypt-chatbot-minimize,
.techrypt-chatbot-close {
  background: none;
  border: none;
  color: rgba(0, 0, 0, 0.8);
  font-size: 14px;
  cursor: pointer;
  padding: 5px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 26px;
  height: 26px;
  flex-shrink: 0;
}

.techrypt-chatbot-clear:hover,
.techrypt-chatbot-minimize:hover,
.techrypt-chatbot-close:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #000000;
  transform: scale(1.05);
}

.techrypt-chatbot-close {
  font-size: 24px;
}

/* Error Alert */
.techrypt-chatbot-error {
  margin: 16px;
  padding: 12px 16px;
  background: rgba(255, 107, 53, 0.1);
  border: 1px solid rgba(255, 107, 53, 0.3);
  border-radius: 8px;
  color: #ff6b35;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.techrypt-chatbot-error button {
  background: none;
  border: none;
  color: #ff6b35;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 12px;
}

/* Messages */
.techrypt-chatbot-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0.05) 100%);
}

.techrypt-message {
  display: flex;
  margin-bottom: 16px;
  gap: 12px;
}

.techrypt-message.user-message {
  flex-direction: row-reverse;
}

.techrypt-message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  flex-shrink: 0;
}

.user-message .techrypt-message-avatar {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
}

.bot-message .techrypt-message-avatar {
  background: linear-gradient(135deg, #ff6b35 0%, #cc4a1a 100%);
}

.techrypt-message-content {
  display: flex;
  flex-direction: column;
  max-width: 80%;
}

.user-message .techrypt-message-content {
  align-items: flex-end;
}

.bot-message .techrypt-message-content {
  align-items: flex-start;
}

.techrypt-message-bubble {
  padding: 16px 20px;
  border-radius: 20px;
  position: relative;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.user-message .techrypt-message-bubble {
  background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
  color: white;
}

.bot-message .techrypt-message-bubble {
  background: linear-gradient(135deg, #2a2a2a 0%, #3a3a3a 100%);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.techrypt-message-bubble p {
  margin: 0;
  line-height: 1.6;
  font-size: 15px;
  white-space: pre-wrap;
}

.techrypt-speak-button {
  background: rgba(0, 212, 255, 0.1);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 8px;
  color: #00d4ff;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s ease;
  align-self: flex-end;
}

.techrypt-speak-button:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: scale(1.05);
}

.techrypt-message-time {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 4px;
  padding: 0 8px;
}

/* Typing Indicator */
.techrypt-typing-indicator {
  display: flex;
  gap: 4px;
  padding: 16px 20px;
  background: linear-gradient(135deg, #2a2a2a 0%, #3a3a3a 100%);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.techrypt-typing-indicator span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #00d4ff;
  animation: typing 1.4s infinite ease-in-out;
}

.techrypt-typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.techrypt-typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 60%, 100% {
    transform: translateY(0);
    opacity: 0.5;
  }
  30% {
    transform: translateY(-10px);
    opacity: 1;
  }
}

/* Suggestions */
.techrypt-chatbot-suggestions {
  padding: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(255, 107, 53, 0.05) 100%);
}

.techrypt-chatbot-suggestions p {
  margin: 0 0 12px 0;
  color: rgba(255, 255, 255, 0.7);
  font-size: 14px;
  font-weight: 500;
}

.techrypt-suggestions-list {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.techrypt-suggestion-chip {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(255, 107, 53, 0.1) 100%);
  border: 1px solid rgba(0, 212, 255, 0.3);
  border-radius: 12px;
  color: white;
  padding: 8px 16px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.techrypt-suggestion-chip:hover {
  background: linear-gradient(135deg, rgba(0, 212, 255, 0.2) 0%, rgba(255, 107, 53, 0.2) 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 212, 255, 0.3);
}

/* Input */
.techrypt-chatbot-input {
  padding: 16px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.1) 100%);
}

.techrypt-input-container {
  display: flex;
  gap: 8px;
  align-items: flex-end;
}

.techrypt-input-container textarea {
  flex: 1;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 16px 20px;
  color: white;
  font-size: 16px;
  font-family: inherit;
  resize: none;
  min-height: 24px;
  max-height: 120px;
  transition: all 0.3s ease;
}

.techrypt-input-container textarea:focus {
  outline: none;
  border-color: #00d4ff;
  border-width: 2px;
  box-shadow: 0 0 0 3px rgba(0, 212, 255, 0.1);
}

.techrypt-input-container textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.techrypt-mic-button,
.techrypt-send-button {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  border: 2px solid rgba(255, 255, 255, 0.1);
  color: white;
  font-size: 18px;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.techrypt-mic-button {
  background: linear-gradient(135deg, #AEBB1E 0%, #D3DC5A 100%);
  box-shadow: 0 4px 20px rgba(174, 187, 30, 0.3);
}

.techrypt-mic-button.listening {
  background: linear-gradient(135deg, #ff6b35 0%, #cc4a1a 100%);
  box-shadow: 0 0 20px rgba(255, 107, 53, 0.5);
}

.techrypt-send-button {
  background: linear-gradient(135deg, #AEBB1E 0%, #D3DC5A 100%);
  box-shadow: 0 4px 20px rgba(174, 187, 30, 0.3);
}

.techrypt-mic-button:hover,
.techrypt-send-button:hover {
  transform: translateY(-2px);
}

.techrypt-mic-button:disabled,
.techrypt-send-button:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Listening Indicator */
.techrypt-listening-indicator {
  margin-top: 16px;
  padding: 16px;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(255, 107, 53, 0.2) 100%);
  border: 1px solid rgba(255, 107, 53, 0.3);
  display: flex;
  align-items: center;
  gap: 12px;
  color: #ff6b35;
  font-size: 14px;
  font-weight: 500;
}

.techrypt-pulse-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ff6b35;
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.7);
  }
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(255, 107, 53, 0);
  }
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(255, 107, 53, 0);
  }
}

/* Form Modal Styles */
.techrypt-form-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 20px;
  z-index: 10000;
}

.techrypt-form-modal {
  background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  margin-top: 20px;
}

.techrypt-appointment-modal {
  max-width: 600px;
}

/* Mobile Form Styles */
@media (max-width: 768px) {
  .techrypt-form-overlay {
    padding: 10px;
    align-items: flex-start;
    justify-content: center;
  }

  .techrypt-form-modal {
    width: 100%;
    max-width: 100%;
    max-height: calc(100vh - 20px);
    border-radius: 12px;
    margin-top: 0;
  }

  .techrypt-appointment-modal {
    max-width: 100%;
  }

  .techrypt-form-header {
    padding: 16px;
  }

  .techrypt-form-header h3 {
    font-size: 18px;
  }

  .techrypt-form-content {
    padding: 16px;
  }

  .techrypt-form-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

.techrypt-form-header {
  padding: 24px;
  background: linear-gradient(135deg, #AEBB1E 0%, #D4FF00 100%);
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 16px 16px 0 0;
}

.techrypt-form-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
}

.techrypt-form-header button {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.techrypt-form-header button:hover {
  background: rgba(255, 255, 255, 0.2);
}

.techrypt-form-content {
  padding: 24px;
}

.techrypt-form-content p {
  margin: 0 0 24px 0;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.techrypt-form-fields {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.techrypt-form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.techrypt-form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.techrypt-form-field label {
  color: white;
  font-weight: 500;
  font-size: 14px;
}

.techrypt-form-field input,
.techrypt-form-field select,
.techrypt-form-field textarea {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px 16px;
  color: white;
  font-size: 14px;
  font-family: inherit;
  transition: all 0.3s ease;
}

.techrypt-form-field input:focus,
.techrypt-form-field select:focus,
.techrypt-form-field textarea:focus {
  outline: none;
  border-color: #AEBB1E;
  box-shadow: 0 0 0 3px rgba(174, 187, 30, 0.1);
}

.techrypt-form-field input::placeholder,
.techrypt-form-field textarea::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.techrypt-form-field select option {
  background: #2a2a2a;
  color: white;
}

.techrypt-form-field textarea {
  resize: vertical;
  min-height: 80px;
}

/* Services grid styling */
.techrypt-services-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  margin-top: 8px;
}

.techrypt-service-checkbox {
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.02);
}

.techrypt-service-checkbox:hover {
  border-color: #AEBB1E;
  background: rgba(174, 187, 30, 0.1);
}

.techrypt-service-checkbox label {
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  margin: 0;
  font-weight: normal;
  color: white;
}

.techrypt-service-checkbox input[type="checkbox"] {
  margin-right: 12px;
  margin-top: 2px;
  transform: scale(1.2);
  accent-color: #AEBB1E;
}

.techrypt-service-content {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.techrypt-service-icon {
  font-size: 18px;
  margin-bottom: 4px;
}

.techrypt-service-name {
  font-weight: 600;
  color: white;
  margin-bottom: 2px;
  font-size: 14px;
}

.techrypt-service-desc {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.3;
}

.techrypt-service-checkbox input[type="checkbox"]:checked + .techrypt-service-content {
  color: #D4FF00;
}

.techrypt-service-checkbox:has(input[type="checkbox"]:checked) {
  border-color: #D4FF00;
  background: rgba(212, 255, 0, 0.1);
}

/* Error states */
.techrypt-form-field input.error,
.techrypt-form-field select.error,
.techrypt-form-field textarea.error {
  border-color: #ff4757;
  background: rgba(255, 71, 87, 0.1);
}

.techrypt-form-field input.error:focus,
.techrypt-form-field select.error:focus,
.techrypt-form-field textarea.error:focus {
  border-color: #ff4757;
  box-shadow: 0 0 0 3px rgba(255, 71, 87, 0.1);
}

.techrypt-field-error {
  display: block;
  color: #ff4757;
  font-size: 12px;
  margin-top: 4px;
  font-weight: 500;
}

.techrypt-business-hours {
  margin: 20px 0;
  padding: 16px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  line-height: 1.5;
}

.techrypt-form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.techrypt-form-cancel,
.techrypt-form-submit {
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.techrypt-form-cancel {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.8);
}

.techrypt-form-cancel:hover {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.techrypt-form-submit {
  background: linear-gradient(135deg, #AEBB1E 0%, #D4FF00 100%);
  color: white;
}

.techrypt-form-submit:hover:not(:disabled) {
  background: linear-gradient(135deg, #D4FF00 0%, #AEBB1E 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(174, 187, 30, 0.3);
}

.techrypt-form-submit:disabled {
  background: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.3);
  cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
  .techrypt-chatbot-overlay {
    padding: 8px;
  }

  .techrypt-chatbot-container {
    height: calc(100vh - 16px);
    max-height: none;
    max-width: 300px;
    margin-right: 8px;
  }

  .techrypt-chatbot-header {
    padding: 14px 14px;
    min-height: 68px;
  }

  .techrypt-chatbot-header-content {
    gap: 8px;
    padding: 14px 2px 2px 2px;
  }

  .techrypt-chatbot-header-actions {
    gap: 6px;
    top: 8px;
    right: 14px;
    padding: 2px 2px 6px 0;
  }

  .techrypt-chatbot-avatar {
    width: 28px;
    height: 28px;
    font-size: 14px;
    margin-right: 2px;
  }

  .techrypt-chatbot-title h3 {
    font-size: 12px;
    margin: 0;
    letter-spacing: 0.1px;
  }

  .techrypt-chatbot-clear,
  .techrypt-chatbot-minimize,
  .techrypt-chatbot-close {
    min-width: 22px;
    height: 22px;
    font-size: 12px;
    padding: 3px;
  }

  .techrypt-chatbot-messages {
    padding: 12px;
  }

  .techrypt-chatbot-input {
    padding: 12px;
  }

  .techrypt-mic-button,
  .techrypt-send-button {
    width: 40px;
    height: 40px;
    font-size: 16px;
  }

  .techrypt-input-container {
    gap: 6px;
  }

  .techrypt-form-overlay {
    padding: 10px;
  }

  .techrypt-form-modal {
    max-height: calc(100vh - 20px);
  }

  .techrypt-form-header,
  .techrypt-form-content {
    padding: 16px;
  }

  .techrypt-form-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .techrypt-form-actions {
    flex-direction: column;
  }

  .techrypt-form-cancel,
  .techrypt-form-submit {
    width: 100%;
  }
}
