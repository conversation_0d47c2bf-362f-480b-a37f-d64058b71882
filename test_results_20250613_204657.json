{"mongodb_atlas": {"status": "success", "details": ["✅ Database connection established", "✅ users: 9 documents", "✅ Sample data retrieved from users", "✅ appointments: 18 documents", "✅ Sample data retrieved from appointments", "✅ conversations: 67 documents", "✅ Sample data retrieved from conversations", "✅ Write operation successful", "✅ Cleanup successful"], "collection_stats": {"users": 9, "appointments": 18, "conversations": 67}}, "email_system": {"status": "success", "details": ["✅ SMTP connection successful", "✅ Test email sent successfully"]}, "csv_export": {"status": "success", "details": ["✅ users: 9 records, 1286 bytes", "✅ appointments: 18 records, 4822 bytes", "✅ conversations: 67 records, 43238 bytes", "✅ CSV email delivery successful"], "files_exported": 3}, "overall": {"status": "success", "timestamp": "2025-06-13T20:46:23.846051"}}