#!/usr/bin/env python3
"""
📊 SIMPLE CSV EXPORT TEST
Test CSV export functionality directly using the database connection
"""

import os
import pandas as pd
from datetime import datetime
from db import get_collection

def test_csv_export():
    """Test CSV export functionality"""
    print("📊 TESTING CSV EXPORT FUNCTIONALITY")
    print("=" * 45)
    
    try:
        # Create export directory
        export_dir = "test_csv_exports"
        os.makedirs(export_dir, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        exported_files = []
        
        # Test Users Export
        print("🔄 Exporting users...")
        users_collection = get_collection('users')
        users = list(users_collection.find({}))
        
        if users:
            # Convert ObjectId to string
            for user in users:
                user['_id'] = str(user['_id'])
            
            users_df = pd.DataFrame(users)
            users_file = f"{export_dir}/users_{timestamp}.csv"
            users_df.to_csv(users_file, index=False)
            exported_files.append(users_file)
            
            file_size = os.path.getsize(users_file)
            print(f"✅ Users: {len(users)} records exported ({file_size:,} bytes)")
        else:
            print("⚠️ No users found")
        
        # Test Appointments Export
        print("🔄 Exporting appointments...")
        appointments_collection = get_collection('appointments')
        appointments = list(appointments_collection.find({}))
        
        if appointments:
            # Convert ObjectId to string
            for apt in appointments:
                apt['_id'] = str(apt['_id'])
                if apt.get('user_id'):
                    apt['user_id'] = str(apt['user_id'])
            
            appointments_df = pd.DataFrame(appointments)
            appointments_file = f"{export_dir}/appointments_{timestamp}.csv"
            appointments_df.to_csv(appointments_file, index=False)
            exported_files.append(appointments_file)
            
            file_size = os.path.getsize(appointments_file)
            print(f"✅ Appointments: {len(appointments)} records exported ({file_size:,} bytes)")
        else:
            print("⚠️ No appointments found")
        
        # Test Conversations Export
        print("🔄 Exporting conversations...")
        conversations_collection = get_collection('conversations')
        conversations = list(conversations_collection.find({}))
        
        if conversations:
            # Convert ObjectId to string
            for conv in conversations:
                conv['_id'] = str(conv['_id'])
            
            conversations_df = pd.DataFrame(conversations)
            conversations_file = f"{export_dir}/conversations_{timestamp}.csv"
            conversations_df.to_csv(conversations_file, index=False)
            exported_files.append(conversations_file)
            
            file_size = os.path.getsize(conversations_file)
            print(f"✅ Conversations: {len(conversations)} records exported ({file_size:,} bytes)")
        else:
            print("⚠️ No conversations found")
        
        # Show export summary
        print(f"\n📊 EXPORT SUMMARY:")
        print(f"✅ Files exported: {len(exported_files)}")
        print(f"📁 Export directory: {export_dir}")
        
        for file_path in exported_files:
            file_size = os.path.getsize(file_path)
            print(f"   • {os.path.basename(file_path)}: {file_size:,} bytes")
        
        # Test file contents
        print(f"\n🔍 TESTING FILE CONTENTS:")
        for file_path in exported_files:
            try:
                df = pd.read_csv(file_path)
                print(f"✅ {os.path.basename(file_path)}: {len(df)} rows, {len(df.columns)} columns")
                if len(df) > 0:
                    print(f"   Columns: {list(df.columns)[:5]}...")  # Show first 5 columns
            except Exception as e:
                print(f"❌ Error reading {os.path.basename(file_path)}: {e}")
        
        print(f"\n🎉 CSV EXPORT TEST COMPLETED SUCCESSFULLY!")
        print(f"✅ All {len(exported_files)} files exported and verified")
        
        # Clean up test files
        cleanup = input("\nDo you want to clean up test files? (y/N): ").lower()
        if cleanup in ['y', 'yes']:
            for file_path in exported_files:
                try:
                    os.remove(file_path)
                    print(f"🗑️ Deleted: {os.path.basename(file_path)}")
                except:
                    pass
            try:
                os.rmdir(export_dir)
                print(f"🗑️ Deleted directory: {export_dir}")
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ CSV export test failed: {e}")
        return False

def main():
    """Main function"""
    print("📊 TECHRYPT CSV EXPORT TESTER")
    print("=" * 50)
    print("Testing direct CSV export functionality")
    print()
    
    success = test_csv_export()
    
    if success:
        print("\n✅ CSV export system is working correctly!")
        print("💡 You can now use the automated export features")
    else:
        print("\n❌ CSV export system has issues")
        print("💡 Check database connection and pandas installation")

if __name__ == "__main__":
    main()
