#!/usr/bin/env python3
"""
🔍 DATABASE UPDATE CHECKER
Check current database status and recent updates
"""

from db import get_collection
from datetime import datetime
import json

def check_database_status():
    """Check current database status"""
    print("🔍 TECHRYPT DATABASE STATUS CHECK")
    print("=" * 50)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # Check each collection
    collections = ['users', 'appointments', 'conversations']
    total_documents = 0
    
    for coll_name in collections:
        try:
            collection = get_collection(coll_name)
            count = collection.count_documents({})
            total_documents += count
            
            print(f"📊 {coll_name.upper()}:")
            print(f"   Total: {count} documents")
            
            # Get latest document
            latest = list(collection.find().sort('_id', -1).limit(1))
            
            if latest:
                doc = latest[0]
                print(f"   Latest ID: {str(doc['_id'])}")
                
                # Show relevant timestamp fields
                if 'created_at' in doc:
                    print(f"   Latest Created: {doc['created_at']}")
                elif 'timestamp' in doc:
                    print(f"   Latest Timestamp: {doc['timestamp']}")
                
                # Show sample data
                if coll_name == 'users' and 'name' in doc:
                    print(f"   Latest User: {doc.get('name', 'Unknown')} ({doc.get('email', 'No email')})")
                elif coll_name == 'appointments' and 'status' in doc:
                    print(f"   Latest Appointment: Status={doc.get('status', 'Unknown')}")
                elif coll_name == 'conversations' and 'user_name' in doc:
                    print(f"   Latest Conversation: User={doc.get('user_name', 'Unknown')}")
            
            print()
            
        except Exception as e:
            print(f"❌ Error checking {coll_name}: {e}")
            print()
    
    print(f"📈 TOTAL DATABASE SIZE: {total_documents} documents")
    return total_documents

def show_recent_updates(collection_name, limit=5):
    """Show recent updates for a specific collection"""
    print(f"\n📋 RECENT {collection_name.upper()} (Last {limit})")
    print("-" * 40)
    
    try:
        collection = get_collection(collection_name)
        recent_docs = list(collection.find().sort('_id', -1).limit(limit))
        
        if not recent_docs:
            print("   No documents found")
            return
        
        for i, doc in enumerate(recent_docs, 1):
            print(f"{i}. ID: {str(doc['_id'])}")
            
            if collection_name == 'users':
                print(f"   Name: {doc.get('name', 'Unknown')}")
                print(f"   Email: {doc.get('email', 'No email')}")
                print(f"   Business: {doc.get('business_type', 'Unknown')}")
                
            elif collection_name == 'appointments':
                print(f"   Status: {doc.get('status', 'Unknown')}")
                print(f"   Services: {', '.join(doc.get('services', []))}")
                print(f"   Date: {doc.get('preferred_date', 'Not set')}")
                
            elif collection_name == 'conversations':
                print(f"   User: {doc.get('user_name', 'Unknown')}")
                print(f"   Message: {doc.get('user_message', '')[:50]}...")
                print(f"   Response: {doc.get('bot_response', '')[:50]}...")
            
            print()
            
    except Exception as e:
        print(f"❌ Error getting recent {collection_name}: {e}")

def check_api_endpoints():
    """Check if API endpoints are working"""
    print("\n🌐 API ENDPOINTS STATUS")
    print("-" * 30)
    
    try:
        import requests
        
        # Test backend endpoints
        endpoints = [
            ("http://localhost:5002/", "Home"),
            ("http://localhost:5002/health", "Health Check"),
            ("http://localhost:5002/users", "Users API")
        ]
        
        for url, name in endpoints:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    print(f"✅ {name}: Working (Status: {response.status_code})")
                    
                    # Show data for users endpoint
                    if 'users' in url:
                        data = response.json()
                        print(f"   Users returned: {data.get('count', 0)}")
                else:
                    print(f"❌ {name}: Error (Status: {response.status_code})")
                    
            except requests.exceptions.ConnectionError:
                print(f"❌ {name}: Server not running")
            except Exception as e:
                print(f"❌ {name}: Error - {e}")
        
        # Test frontend
        try:
            response = requests.get("http://localhost:5173", timeout=5)
            if response.status_code == 200:
                print("✅ Frontend: Running (React + Vite)")
            else:
                print(f"❌ Frontend: Error (Status: {response.status_code})")
        except requests.exceptions.ConnectionError:
            print("❌ Frontend: Not running")
        except Exception as e:
            print(f"❌ Frontend: Error - {e}")
            
    except ImportError:
        print("⚠️ Requests module not available for API testing")

def main():
    """Main function"""
    print("🔍 TECHRYPT DATABASE & SYSTEM CHECKER")
    print("=" * 60)
    print("Checking current database status and recent updates")
    print()
    
    # Check database status
    total_docs = check_database_status()
    
    # Show recent updates for each collection
    show_recent_updates('users', 3)
    show_recent_updates('appointments', 3)
    show_recent_updates('conversations', 3)
    
    # Check API endpoints
    check_api_endpoints()
    
    print("\n" + "=" * 60)
    print("📊 DATABASE CHECK COMPLETE")
    print(f"✅ Total documents in database: {total_docs}")
    print("✅ MongoDB Atlas connection working")
    print("💡 Use this script anytime to check database updates")

if __name__ == "__main__":
    main()
