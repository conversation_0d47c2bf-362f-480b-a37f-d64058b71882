.marketing-container {
  display: flex;
  justify-content: space-between;
  padding: 20px;
  height: 100vh;
}

.marketing-card {
  flex: 1;
  padding: 30px 40px;
  margin: 10px;
  color: #fff;
  border: 2px solid var(--color-white);
  border-radius: 30px;
  position: relative; /* Positioning context for price tag and button */
}

.marketing-card h1 {
  margin-bottom: 20px;
  font-size: 54px;
  color: var(--color-white);
}

.marketing-card p {
  margin-bottom: 20px;
  font-size: 26px;
  margin-bottom: 10px;
}

ul {
  padding: 0;
}

li {
  margin-bottom: 5px;
  line-height: 20px;
}

.marketing-card:nth-child(2) {
  background: var(--color-yellow);
  border: none;
}

/* New styling for button and price tag */
.bottom-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: absolute;
  bottom: 20px;
  left: 30px;
  right: 30px;
}

.price-tag {
  font-size: 34px;
  color: var(--color-white);
}

.buy-btn {
  background-color: white;
  color: black;
  border: none;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 18px;
  cursor: pointer;
}

.buy-btn:hover {
  background-color: rgb(196, 211, 177);
}

@media (max-width: 768px) {
  .marketing-container {
    flex-direction: column;
    height: auto;
  }
  .marketing-card {
    padding-bottom: 70px;
  }
  .marketing-card h1 {
    font-size: 2.5rem;
  }
}
