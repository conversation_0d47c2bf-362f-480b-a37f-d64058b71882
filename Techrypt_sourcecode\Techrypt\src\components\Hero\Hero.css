.hero-section {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  height: 100vh;
  overflow: hidden;
  color: var(--color-white);
}

.background-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.outer-headings,
.hero-para,
.container {
  position: relative;
  z-index: 1;
}

.outer-headings {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.inner-headings {
  border: 0 solid #ddd;
  height: 150px;
  line-height: 150px;
  font-size: 145px;
  text-transform: uppercase;
  overflow: hidden;
  transition: opacity 0.5s ease;
  font-weight: bold;
}

.fade-in {
  opacity: 1;
  animation: popAnimation 0.5s ease;
}

.fade-out {
  opacity: 0.75;
}
.hero-para p {
  color: var(--color-yellow);
  font-size: 25px;
  margin-bottom: 6.1rem;
}

@keyframes popAnimation {
  0%,
  100% {
    transform: scale(1) translateY(0);
  }
  10% {
    transform: scale(1.1) translateY(-5px);
  }
  20% {
    transform: scale(1) translateY(0);
  }
}

.smile {
  width: 2.4rem;
  height: 2.4rem;
  background: 0 0 / 100% repeat-y;
  background-image: url("../../assets/svgs/smile.svg");
  border-radius: 50%;
  position: relative;
  border-radius: 50%;
  transition: background-position var(--tr-main-dur) var(--tr-main-tf);
}
.smile:hover {
  background-position-y: -2.4rem;
}

@media only screen and (max-width: 600px) {
  .hero-section {
    height: 90vh;
  }
  .inner-headings {
    height: 50px;
    line-height: 50px;
    font-size: 45px;
    margin-top: 120px;
  }
  .hero-para {
    width: 75%;
  }
  .hero-para p {
    font-size: 15px;
    margin-bottom: 4rem;
  }
}
