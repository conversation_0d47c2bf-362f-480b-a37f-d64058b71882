{"name": "zorka", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emailjs/browser": "^4.4.1", "@fortawesome/fontawesome-free": "^6.6.0", "@tailwindcss/vite": "^4.1.4", "axios": "^1.6.0", "framer-motion": "^11.18.2", "react": "^18.3.1", "react-calendly": "^4.3.1", "react-dom": "^18.3.1", "react-icons": "^5.5.0", "react-loader-spinner": "^6.1.6", "react-modal": "^3.16.3", "react-router-dom": "^6.26.2", "react-slick": "^0.30.2", "react-toastify": "^11.0.5", "react-transition-group": "^4.4.5", "slick-carousel": "^1.8.1", "swiper": "^11.1.14"}, "devDependencies": {"@eslint/js": "^9.9.0", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.21", "eslint": "^9.9.0", "eslint-plugin-react": "^7.35.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "vite": "^5.4.19"}}