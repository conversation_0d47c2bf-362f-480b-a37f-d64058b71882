#!/usr/bin/env python3
"""
🧪 COMPREHENSIVE TECHRYPT SYSTEM TESTER
Test MongoDB Atlas connection, Email system, and CSV export functionality
"""

import os
import sys
import json
import smtplib
import pandas as pd
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import local modules
try:
    from db import get_collection, get_db
    print("✅ Successfully imported database modules")
except ImportError as e:
    print(f"❌ Failed to import database modules: {e}")
    sys.exit(1)

class TechryptSystemTester:
    """Comprehensive system tester for Techrypt"""
    
    def __init__(self):
        """Initialize the tester"""
        self.results = {
            'mongodb_atlas': {'status': 'pending', 'details': []},
            'email_system': {'status': 'pending', 'details': []},
            'csv_export': {'status': 'pending', 'details': []},
            'overall': {'status': 'pending', 'timestamp': datetime.now().isoformat()}
        }
        
        # Email configuration
        self.sender_email = os.getenv("SENDER_EMAIL", "")
        self.sender_password = os.getenv("EMAIL_PASSWORD", "")
        self.admin_email = os.getenv("ADMIN_EMAIL", "<EMAIL>")
        
        # SMTP configuration
        self.smtp_server = os.getenv("CUSTOM_SMTP_SERVER", "smtp.gmail.com")
        self.smtp_port = int(os.getenv("CUSTOM_SMTP_PORT", "587"))
        
        print("🧪 Techrypt System Tester Initialized")
        print("=" * 50)
    
    def test_mongodb_atlas_connection(self):
        """Test MongoDB Atlas connection and basic operations"""
        print("\n🔍 TESTING MONGODB ATLAS CONNECTION")
        print("-" * 40)
        
        try:
            # Test basic connection
            db = get_db()
            if db is None:
                raise Exception("Database connection returned None")
            
            self.results['mongodb_atlas']['details'].append("✅ Database connection established")
            print("✅ Database connection established")
            
            # Test collections access
            collections_to_test = ['users', 'appointments', 'conversations']
            collection_stats = {}
            
            for collection_name in collections_to_test:
                try:
                    collection = get_collection(collection_name)
                    count = collection.count_documents({})
                    collection_stats[collection_name] = count
                    
                    self.results['mongodb_atlas']['details'].append(f"✅ {collection_name}: {count} documents")
                    print(f"✅ {collection_name} collection: {count} documents")
                    
                    # Test sample data retrieval
                    if count > 0:
                        sample = collection.find_one()
                        if sample:
                            self.results['mongodb_atlas']['details'].append(f"✅ Sample data retrieved from {collection_name}")
                            print(f"✅ Sample data retrieved from {collection_name}")
                    
                except Exception as e:
                    self.results['mongodb_atlas']['details'].append(f"❌ {collection_name} error: {str(e)}")
                    print(f"❌ Error accessing {collection_name}: {e}")
            
            # Test write operation (insert and delete a test document)
            try:
                test_collection = get_collection('test_connection')
                test_doc = {
                    'test': True,
                    'timestamp': datetime.now().isoformat(),
                    'purpose': 'connection_test'
                }
                
                # Insert test document
                result = test_collection.insert_one(test_doc)
                if result.inserted_id:
                    self.results['mongodb_atlas']['details'].append("✅ Write operation successful")
                    print("✅ Write operation successful")
                    
                    # Clean up test document
                    test_collection.delete_one({'_id': result.inserted_id})
                    self.results['mongodb_atlas']['details'].append("✅ Cleanup successful")
                    print("✅ Test document cleaned up")
                
            except Exception as e:
                self.results['mongodb_atlas']['details'].append(f"⚠️ Write test failed: {str(e)}")
                print(f"⚠️ Write test failed: {e}")
            
            self.results['mongodb_atlas']['status'] = 'success'
            self.results['mongodb_atlas']['collection_stats'] = collection_stats
            
            print(f"\n🎉 MongoDB Atlas connection test PASSED")
            return True
            
        except Exception as e:
            self.results['mongodb_atlas']['status'] = 'failed'
            self.results['mongodb_atlas']['details'].append(f"❌ Connection failed: {str(e)}")
            print(f"❌ MongoDB Atlas connection test FAILED: {e}")
            return False
    
    def test_email_system(self):
        """Test email system configuration and connectivity"""
        print("\n📧 TESTING EMAIL SYSTEM")
        print("-" * 30)
        
        # Check configuration
        config_issues = []
        
        if not self.sender_email or self.sender_email == "<EMAIL>":
            config_issues.append("SENDER_EMAIL not configured")
        
        if not self.sender_password or self.sender_password == "your-app-password":
            config_issues.append("EMAIL_PASSWORD not configured")
        
        if config_issues:
            self.results['email_system']['status'] = 'failed'
            self.results['email_system']['details'].extend([f"❌ {issue}" for issue in config_issues])
            for issue in config_issues:
                print(f"❌ {issue}")
            print("💡 Please configure your .env file with email settings")
            return False
        
        print(f"📧 Sender: {self.sender_email}")
        print(f"🔌 SMTP: {self.smtp_server}:{self.smtp_port}")
        print(f"📬 Admin: {self.admin_email}")
        
        try:
            # Test SMTP connection
            print("🔄 Testing SMTP connection...")
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.sender_email, self.sender_password)
            server.quit()
            
            self.results['email_system']['details'].append("✅ SMTP connection successful")
            print("✅ SMTP connection successful")
            
            # Send test email
            print("🔄 Sending test email...")
            success = self._send_test_email()
            
            if success:
                self.results['email_system']['status'] = 'success'
                self.results['email_system']['details'].append("✅ Test email sent successfully")
                print("✅ Test email sent successfully")
                print(f"💡 Check {self.admin_email} for the test email")
                return True
            else:
                self.results['email_system']['status'] = 'failed'
                return False
                
        except Exception as e:
            self.results['email_system']['status'] = 'failed'
            self.results['email_system']['details'].append(f"❌ Email test failed: {str(e)}")
            print(f"❌ Email system test FAILED: {e}")
            return False
    
    def _send_test_email(self):
        """Send a test email"""
        try:
            msg = MIMEText(f"""
This is a test email from the Techrypt System Tester.

✅ MongoDB Atlas Connection: Working
✅ Email System: Working  
✅ System Test: Completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

Configuration Details:
- SMTP Server: {self.smtp_server}:{self.smtp_port}
- Sender: {self.sender_email}
- Recipient: {self.admin_email}

The system is ready for automated exports and notifications.

Best regards,
Techrypt System Tester
            """)
            
            msg['Subject'] = f"Techrypt System Test - {datetime.now().strftime('%Y-%m-%d %H:%M')}"
            msg['From'] = self.sender_email
            msg['To'] = self.admin_email
            
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.sender_email, self.sender_password)
            server.send_message(msg)
            server.quit()
            
            return True

        except Exception as e:
            self.results['email_system']['details'].append(f"❌ Test email failed: {str(e)}")
            print(f"❌ Test email failed: {e}")
            return False

    def test_csv_export_system(self):
        """Test CSV export functionality"""
        print("\n📊 TESTING CSV EXPORT SYSTEM")
        print("-" * 35)

        try:
            # Create test export directory
            export_dir = "test_exports"
            os.makedirs(export_dir, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_test")
            exported_files = []

            # Test exporting each collection
            collections_to_export = ['users', 'appointments', 'conversations']

            for collection_name in collections_to_export:
                try:
                    print(f"🔄 Exporting {collection_name}...")

                    collection = get_collection(collection_name)
                    documents = list(collection.find({}).limit(100))  # Limit for testing

                    if documents:
                        # Convert to DataFrame and export to CSV
                        df = pd.DataFrame(documents)

                        # Handle ObjectId conversion
                        if '_id' in df.columns:
                            df['_id'] = df['_id'].astype(str)

                        csv_file = f"{export_dir}/{collection_name}_{timestamp}.csv"
                        df.to_csv(csv_file, index=False)

                        if os.path.exists(csv_file):
                            file_size = os.path.getsize(csv_file)
                            exported_files.append(csv_file)

                            self.results['csv_export']['details'].append(f"✅ {collection_name}: {len(documents)} records, {file_size} bytes")
                            print(f"✅ {collection_name}: {len(documents)} records exported ({file_size} bytes)")
                        else:
                            raise Exception(f"CSV file not created for {collection_name}")
                    else:
                        self.results['csv_export']['details'].append(f"⚠️ {collection_name}: No data to export")
                        print(f"⚠️ {collection_name}: No data found")

                except Exception as e:
                    self.results['csv_export']['details'].append(f"❌ {collection_name} export failed: {str(e)}")
                    print(f"❌ {collection_name} export failed: {e}")

            # Test email delivery of CSV files (if email system is working)
            if exported_files and self.results['email_system']['status'] == 'success':
                print("🔄 Testing CSV email delivery...")
                email_success = self._send_csv_test_email(exported_files, timestamp)

                if email_success:
                    self.results['csv_export']['details'].append("✅ CSV email delivery successful")
                    print("✅ CSV email delivery successful")
                else:
                    self.results['csv_export']['details'].append("❌ CSV email delivery failed")
                    print("❌ CSV email delivery failed")

            # Clean up test files
            for file_path in exported_files:
                try:
                    os.remove(file_path)
                    print(f"🗑️ Cleaned up: {os.path.basename(file_path)}")
                except:
                    pass

            try:
                os.rmdir(export_dir)
            except:
                pass

            if exported_files:
                self.results['csv_export']['status'] = 'success'
                self.results['csv_export']['files_exported'] = len(exported_files)
                print(f"\n🎉 CSV export test PASSED ({len(exported_files)} files)")
                return True
            else:
                self.results['csv_export']['status'] = 'failed'
                print(f"\n❌ CSV export test FAILED (no files exported)")
                return False

        except Exception as e:
            self.results['csv_export']['status'] = 'failed'
            self.results['csv_export']['details'].append(f"❌ Export system failed: {str(e)}")
            print(f"❌ CSV export test FAILED: {e}")
            return False

    def _send_csv_test_email(self, csv_files, timestamp):
        """Send test email with CSV attachments"""
        try:
            msg = MIMEMultipart()
            msg['From'] = self.sender_email
            msg['To'] = self.admin_email
            msg['Subject'] = f"Techrypt CSV Export Test - {timestamp}"

            body = f"""
Dear Admin,

This is a test of the CSV export system.

📊 EXPORT SUMMARY:
- Files exported: {len(csv_files)}
- Timestamp: {timestamp}
- Test completed: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📁 ATTACHED FILES:
"""
            for file_path in csv_files:
                if os.path.exists(file_path):
                    file_size = os.path.getsize(file_path)
                    body += f"• {os.path.basename(file_path)} ({file_size:,} bytes)\n"

            body += """
✅ CSV export system is working correctly.

Best regards,
Techrypt System Tester
            """

            msg.attach(MIMEText(body, 'plain'))

            # Attach CSV files
            for file_path in csv_files:
                if os.path.exists(file_path):
                    with open(file_path, "rb") as attachment:
                        part = MIMEBase('application', 'octet-stream')
                        part.set_payload(attachment.read())

                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {os.path.basename(file_path)}'
                    )
                    msg.attach(part)

            # Send email
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.sender_email, self.sender_password)
            server.send_message(msg)
            server.quit()

            return True

        except Exception as e:
            print(f"❌ CSV email test failed: {e}")
            return False

    def run_all_tests(self):
        """Run all system tests"""
        print("🚀 STARTING COMPREHENSIVE SYSTEM TEST")
        print("=" * 60)

        # Run tests in order
        mongodb_success = self.test_mongodb_atlas_connection()
        email_success = self.test_email_system()
        csv_success = self.test_csv_export_system()

        # Calculate overall status
        all_passed = mongodb_success and email_success and csv_success

        if all_passed:
            self.results['overall']['status'] = 'success'
        else:
            self.results['overall']['status'] = 'failed'

        # Print summary
        self.print_test_summary()

        return all_passed

    def print_test_summary(self):
        """Print comprehensive test summary"""
        print("\n" + "=" * 60)
        print("📋 COMPREHENSIVE TEST SUMMARY")
        print("=" * 60)

        # MongoDB Atlas Results
        mongodb_status = self.results['mongodb_atlas']['status']
        mongodb_icon = "✅" if mongodb_status == 'success' else "❌"
        print(f"{mongodb_icon} MongoDB Atlas Connection: {mongodb_status.upper()}")

        if 'collection_stats' in self.results['mongodb_atlas']:
            stats = self.results['mongodb_atlas']['collection_stats']
            for collection, count in stats.items():
                print(f"   • {collection}: {count} documents")

        # Email System Results
        email_status = self.results['email_system']['status']
        email_icon = "✅" if email_status == 'success' else "❌"
        print(f"{email_icon} Email System: {email_status.upper()}")

        # CSV Export Results
        csv_status = self.results['csv_export']['status']
        csv_icon = "✅" if csv_status == 'success' else "❌"
        print(f"{csv_icon} CSV Export System: {csv_status.upper()}")

        if 'files_exported' in self.results['csv_export']:
            print(f"   • Files exported: {self.results['csv_export']['files_exported']}")

        # Overall Status
        overall_status = self.results['overall']['status']
        overall_icon = "🎉" if overall_status == 'success' else "❌"
        print(f"\n{overall_icon} OVERALL STATUS: {overall_status.upper()}")

        if overall_status == 'success':
            print("\n✅ All systems are working correctly!")
            print("✅ Your Techrypt application is ready for production use.")
            print("\n🚀 NEXT STEPS:")
            print("1. Run automated exports: python automated_weekly_export.py")
            print("2. Start the Flask app: python app.py")
            print("3. Schedule weekly exports using the provided batch files")
        else:
            print("\n❌ Some systems need attention.")
            print("💡 Please check the error details above and fix configuration issues.")

        # Save results to file
        results_file = f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        try:
            with open(results_file, 'w') as f:
                json.dump(self.results, f, indent=2)
            print(f"\n📄 Detailed results saved to: {results_file}")
        except Exception as e:
            print(f"⚠️ Could not save results file: {e}")

def main():
    """Main function to run the comprehensive test"""
    print("🧪 TECHRYPT COMPREHENSIVE SYSTEM TESTER")
    print("Testing MongoDB Atlas, Email System, and CSV Export")
    print("=" * 60)

    # Check for .env file
    if not os.path.exists('.env'):
        print("⚠️ No .env file found!")
        print("💡 Create a .env file with your configuration:")
        print("""
# MongoDB Atlas Configuration
MONGODB_URI=mongodb+srv://username:<EMAIL>/
MONGODB_DATABASE=your_database_name

# Email Configuration
SENDER_EMAIL=<EMAIL>
EMAIL_PASSWORD=your-app-password
ADMIN_EMAIL=<EMAIL>

# Optional: Custom SMTP
CUSTOM_SMTP_SERVER=smtp.gmail.com
CUSTOM_SMTP_PORT=587
        """)

        response = input("\nDo you want to continue with default settings? (y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("❌ Test cancelled. Please create .env file first.")
            return

    # Initialize and run tests
    tester = TechryptSystemTester()
    success = tester.run_all_tests()

    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
