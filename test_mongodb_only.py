#!/usr/bin/env python3
"""
🧪 MONGODB ATLAS CONNECTION TESTER
Test only the MongoDB Atlas connection without email dependencies
"""

import os
import sys
from dotenv import load_dotenv
from pymongo import MongoClient

def test_mongodb_connection():
    """Test MongoDB Atlas connection"""
    print("🧪 TESTING MONGODB ATLAS CONNECTION")
    print("=" * 45)
    
    # Load environment variables
    load_dotenv()
    
    # Get MongoDB configuration
    mongodb_uri = os.getenv('MONGODB_URI')
    mongodb_database = os.getenv('MONGODB_DATABASE', 'techrypt_chatbot')
    
    print(f"📊 Database: {mongodb_database}")
    print(f"🔗 URI: {mongodb_uri[:50]}..." if mongodb_uri else "❌ No URI found")
    
    # Check if URI is configured
    if not mongodb_uri or mongodb_uri == "mongodb+srv://your-username:<EMAIL>/":
        print("\n❌ MONGODB_URI not configured in .env file")
        print("\n💡 TO FIX THIS:")
        print("1. Open the .env file in your project")
        print("2. Replace the MONGODB_URI value with your actual Atlas connection string")
        print("3. Get your connection string from MongoDB Atlas dashboard")
        print("4. Format: mongodb+srv://username:<EMAIL>/")
        return False
    
    try:
        print("\n🔄 Connecting to MongoDB Atlas...")
        
        # Create MongoDB client
        client = MongoClient(mongodb_uri)
        
        # Test the connection
        client.admin.command('ping')
        print("✅ Successfully connected to MongoDB Atlas!")
        
        # Get the database
        db = client[mongodb_database]
        print(f"✅ Connected to database: {mongodb_database}")
        
        # Test collections
        print("\n📊 CHECKING COLLECTIONS:")
        collections_to_check = ['users', 'appointments', 'conversations']
        
        for collection_name in collections_to_check:
            try:
                collection = db[collection_name]
                count = collection.count_documents({})
                print(f"   ✅ {collection_name}: {count} documents")
                
                # Show sample data if available
                if count > 0:
                    sample = collection.find_one()
                    if sample:
                        print(f"      📄 Sample fields: {list(sample.keys())[:5]}")
                
            except Exception as e:
                print(f"   ❌ {collection_name}: Error - {e}")
        
        # Test write operation
        print("\n🔄 Testing write operation...")
        test_collection = db['connection_test']
        
        # Insert test document
        test_doc = {
            'test': True,
            'message': 'MongoDB Atlas connection test',
            'timestamp': '2024-01-15'
        }
        
        result = test_collection.insert_one(test_doc)
        if result.inserted_id:
            print("✅ Write operation successful")
            
            # Clean up test document
            test_collection.delete_one({'_id': result.inserted_id})
            print("✅ Test document cleaned up")
        
        # Close connection
        client.close()
        
        print("\n🎉 MONGODB ATLAS CONNECTION TEST PASSED!")
        print("✅ Your database is properly connected and working")
        print("\n🚀 NEXT STEPS:")
        print("1. Configure email settings in .env file (optional)")
        print("2. Run: python comprehensive_system_test.py")
        print("3. Test your application: python app.py")
        
        return True
        
    except Exception as e:
        print(f"\n❌ MONGODB ATLAS CONNECTION FAILED!")
        print(f"Error: {e}")
        
        print("\n🔧 TROUBLESHOOTING:")
        print("1. Check your MongoDB Atlas connection string")
        print("2. Verify username and password are correct")
        print("3. Ensure your IP is whitelisted (or use 0.0.0.0/0)")
        print("4. Check if your cluster is running")
        print("5. Verify network connectivity")
        
        return False

def show_env_status():
    """Show current .env file status"""
    print("📄 CURRENT .ENV FILE STATUS:")
    print("-" * 30)
    
    if not os.path.exists('.env'):
        print("❌ .env file not found")
        return
    
    load_dotenv()
    
    # Check required variables
    required_vars = {
        'MONGODB_URI': 'MongoDB Atlas connection string',
        'MONGODB_DATABASE': 'Database name'
    }
    
    for var, description in required_vars.items():
        value = os.getenv(var, '')
        if value and 'your-' not in value:
            print(f"✅ {var}: Configured")
        else:
            print(f"❌ {var}: Not configured or using default")

def main():
    """Main function"""
    print("🧪 TECHRYPT MONGODB ATLAS TESTER")
    print("=" * 50)
    print("This tool tests only your MongoDB Atlas connection.")
    print()
    
    # Show current status
    show_env_status()
    print()
    
    # Run the test
    success = test_mongodb_connection()
    
    if not success:
        print("\n📝 NEED HELP SETTING UP?")
        print("1. Check the .env file in your project directory")
        print("2. Update MONGODB_URI with your Atlas connection string")
        print("3. Run this test again")
        
        print("\n🔗 GET YOUR CONNECTION STRING:")
        print("1. Go to https://cloud.mongodb.com/")
        print("2. Login to your account")
        print("3. Go to your cluster")
        print("4. Click 'Connect' → 'Connect your application'")
        print("5. Copy the connection string")
        print("6. Replace <password> with your actual password")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
