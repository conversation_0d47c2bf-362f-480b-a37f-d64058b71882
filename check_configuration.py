#!/usr/bin/env python3
"""
🔍 TECHRYPT CONFIGURATION CHECKER
Check current system configuration and help set up missing components
"""

import os
import sys
from dotenv import load_dotenv

def check_env_file():
    """Check if .env file exists and what's configured"""
    print("🔍 CHECKING CONFIGURATION FILES")
    print("=" * 40)
    
    env_exists = os.path.exists('.env')
    print(f"📄 .env file: {'✅ Found' if env_exists else '❌ Missing'}")
    
    if env_exists:
        print("\n📋 Current .env contents:")
        try:
            with open('.env', 'r') as f:
                lines = f.readlines()
                for i, line in enumerate(lines, 1):
                    # Hide sensitive values
                    if '=' in line and not line.strip().startswith('#'):
                        key, value = line.split('=', 1)
                        if any(sensitive in key.upper() for sensitive in ['PASSWORD', 'URI', 'SECRET']):
                            print(f"   {i:2d}: {key}=***HIDDEN***")
                        else:
                            print(f"   {i:2d}: {line.strip()}")
                    else:
                        print(f"   {i:2d}: {line.strip()}")
        except Exception as e:
            print(f"❌ Error reading .env file: {e}")
    
    return env_exists

def check_environment_variables():
    """Check what environment variables are currently set"""
    print("\n🌍 CHECKING ENVIRONMENT VARIABLES")
    print("-" * 35)
    
    # Load .env file
    load_dotenv()
    
    required_vars = {
        'MONGODB_URI': 'MongoDB Atlas connection string',
        'MONGODB_DATABASE': 'Database name (usually techrypt_chatbot)',
        'SENDER_EMAIL': 'Email for sending reports',
        'EMAIL_PASSWORD': 'Email password or app password',
        'ADMIN_EMAIL': 'Admin email for receiving reports'
    }
    
    optional_vars = {
        'CUSTOM_SMTP_SERVER': 'Custom SMTP server',
        'CUSTOM_SMTP_PORT': 'Custom SMTP port'
    }
    
    print("📋 Required Variables:")
    missing_required = []
    for var, description in required_vars.items():
        value = os.getenv(var, '')
        if value and value not in ['<EMAIL>', 'your-app-password', 'your-database-name']:
            print(f"   ✅ {var}: Configured")
        else:
            print(f"   ❌ {var}: Missing or default value")
            missing_required.append(var)
    
    print("\n📋 Optional Variables:")
    for var, description in optional_vars.items():
        value = os.getenv(var, '')
        if value:
            print(f"   ✅ {var}: {value}")
        else:
            print(f"   ⚪ {var}: Not set (will use defaults)")
    
    return missing_required

def create_env_template():
    """Create a .env template file"""
    print("\n📝 CREATING .env TEMPLATE")
    print("-" * 25)
    
    template_content = """# Techrypt System Configuration
# Update these values with your actual settings

# ===== MONGODB ATLAS CONFIGURATION =====
# Get this from your MongoDB Atlas dashboard
# Format: mongodb+srv://username:<EMAIL>/
MONGODB_URI=mongodb+srv://your-username:<EMAIL>/
MONGODB_DATABASE=techrypt_chatbot

# ===== EMAIL CONFIGURATION =====
# Email account that will send automated reports
SENDER_EMAIL=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Admin email (where reports will be sent)
ADMIN_EMAIL=<EMAIL>

# ===== OPTIONAL SMTP CONFIGURATION =====
# Leave blank to auto-detect based on sender email domain
CUSTOM_SMTP_SERVER=smtp.gmail.com
CUSTOM_SMTP_PORT=587

# ===== GMAIL SETUP INSTRUCTIONS =====
# For Gmail users:
# 1. Enable 2-factor authentication
# 2. Go to Google Account > Security > App passwords
# 3. Generate an "App password" for Mail
# 4. Use the app password (not regular password) above
"""
    
    try:
        with open('.env.template', 'w') as f:
            f.write(template_content)
        print("✅ Created .env.template file")
        print("💡 Copy this to .env and update with your values")
        return True
    except Exception as e:
        print(f"❌ Failed to create template: {e}")
        return False

def interactive_setup():
    """Interactive setup for .env file"""
    print("\n🛠️ INTERACTIVE SETUP")
    print("-" * 20)
    
    print("Let's set up your .env file step by step.")
    print("Press Enter to skip optional fields.\n")
    
    # MongoDB Atlas
    print("📊 MONGODB ATLAS SETUP:")
    mongodb_uri = input("Enter your MongoDB Atlas connection string: ").strip()
    if not mongodb_uri:
        print("⚠️ MongoDB URI is required. You can add it later to .env file.")
        mongodb_uri = "mongodb+srv://your-username:<EMAIL>/"
    
    mongodb_db = input("Enter database name [techrypt_chatbot]: ").strip()
    if not mongodb_db:
        mongodb_db = "techrypt_chatbot"
    
    # Email setup
    print("\n📧 EMAIL SETUP:")
    sender_email = input("Enter sender email address: ").strip()
    if not sender_email:
        sender_email = "<EMAIL>"
    
    email_password = input("Enter email password (or app password for Gmail): ").strip()
    if not email_password:
        email_password = "your-app-password"
    
    admin_email = input("Enter admin email [<EMAIL>]: ").strip()
    if not admin_email:
        admin_email = "<EMAIL>"
    
    # Create .env file
    env_content = f"""# Techrypt System Configuration
# Generated on {os.popen('date').read().strip()}

# MongoDB Atlas Configuration
MONGODB_URI={mongodb_uri}
MONGODB_DATABASE={mongodb_db}

# Email Configuration
SENDER_EMAIL={sender_email}
EMAIL_PASSWORD={email_password}
ADMIN_EMAIL={admin_email}

# SMTP Configuration (auto-detected based on sender email)
CUSTOM_SMTP_SERVER=smtp.gmail.com
CUSTOM_SMTP_PORT=587
"""
    
    try:
        with open('.env', 'w') as f:
            f.write(env_content)
        print("\n✅ Created .env file successfully!")
        print("🔒 Remember to keep your .env file secure and don't commit it to version control.")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def main():
    """Main configuration checker"""
    print("🔍 TECHRYPT CONFIGURATION CHECKER")
    print("=" * 50)
    print("This tool helps you set up your system configuration.")
    print()
    
    # Check current configuration
    env_exists = check_env_file()
    missing_vars = check_environment_variables()
    
    # Provide recommendations
    print("\n💡 RECOMMENDATIONS")
    print("-" * 20)
    
    if not env_exists:
        print("❌ No .env file found")
        print("📝 You need to create a .env file with your configuration")
        
        choice = input("\nWould you like to create .env file now? (y/N): ").lower()
        if choice in ['y', 'yes']:
            interactive_setup()
        else:
            create_env_template()
            print("\n📋 Next steps:")
            print("1. Copy .env.template to .env")
            print("2. Update .env with your actual values")
            print("3. Run this script again to verify")
    
    elif missing_vars:
        print(f"⚠️ Missing {len(missing_vars)} required variables:")
        for var in missing_vars:
            print(f"   - {var}")
        
        print("\n📋 Next steps:")
        print("1. Edit your .env file")
        print("2. Add the missing variables")
        print("3. Run this script again to verify")
    
    else:
        print("✅ Configuration looks good!")
        print("🧪 Ready to run system tests")
        
        choice = input("\nWould you like to run the comprehensive system test now? (y/N): ").lower()
        if choice in ['y', 'yes']:
            print("\n🚀 Starting comprehensive system test...")
            os.system("python comprehensive_system_test.py")

if __name__ == "__main__":
    main()
