# Techrypt System Configuration
# Copy this file to .env and update with your actual values

# ===== MONGODB ATLAS CONFIGURATION =====
# Get these from your MongoDB Atlas dashboard
MONGODB_URI=mongodb+srv://username:<EMAIL>/
MONGODB_DATABASE=your_database_name

# ===== EMAIL CONFIGURATION =====
# Email account that will send the automated reports
SENDER_EMAIL=<EMAIL>
EMAIL_PASSWORD=your-app-password

# Admin email (where reports will be sent)
ADMIN_EMAIL=<EMAIL>

# ===== SMTP CONFIGURATION (Optional) =====
# Leave blank to auto-detect based on sender email domain
# Or specify custom SMTP settings
CUSTOM_SMTP_SERVER=smtp.gmail.com
CUSTOM_SMTP_PORT=587

# ===== GMAIL SETUP INSTRUCTIONS =====
# For Gmail users:
# 1. Enable 2-factor authentication on your Google account
# 2. Go to Google Account settings > Security > App passwords
# 3. Generate an "App password" for "Mail"
# 4. Use the generated app password (not your regular password) in EMAIL_PASSWORD

# ===== OTHER EMAIL PROVIDERS =====
# Outlook/Hotmail: smtp-mail.outlook.com:587
# Yahoo: smtp.mail.yahoo.com:587
# Business email: Contact your IT department for SMTP settings
