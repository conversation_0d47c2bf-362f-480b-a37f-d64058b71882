# MONGODB CONFIGURATION
# =====================

# MongoDB Atlas Connection (Cloud)
# Replace with your actual Atlas connection string
MONGODB_URI=mongodb+srv://username:<EMAIL>/techrypt_chatbot?retryWrites=true&w=majority

# Local MongoDB Connection (Fallback)
MONGODB_LOCAL_URI=mongodb://localhost:27017/

# Database Name
MONGODB_DATABASE=techrypt_chatbot

# Email Configuration (for exports)
SMTP_SERVER=smtp.techrypt.io
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=Monday@!23456
SENDER_EMAIL=<EMAIL>

# Application Settings
DEBUG=False
LOG_LEVEL=INFO
